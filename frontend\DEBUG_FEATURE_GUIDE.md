# 🔍 调试功能使用指南

## 🎯 功能概述

新增的调试按钮可以在控制台打印详细的时间轴配置信息，帮助开发者和用户了解当前的时间轴状态。

## 📍 调试按钮位置

- **位置**: 时间轴工具栏右侧
- **图标**: 🔍 放大镜图标
- **样式**: 灰色虚线边框，区别于其他功能按钮
- **提示**: 鼠标悬停显示"在控制台打印时间轴配置信息"

## 📊 调试信息内容

### 1. 基本配置
```
📊 基本配置
├── 时间轴宽度 (像素): 800
├── 总时长 (秒): 60
├── 内容结束时间 (秒): 25.5
├── 像素/秒比例: 13.33
├── 当前播放时间 (秒): 10.2
├── 播放状态: 播放中/已暂停
└── 播放速度: 1x
```

### 2. 视频片段信息
```
🎞️ 视频片段信息 (3 个)
├── 片段 1: video1.mp4
│   ├── ID: 1234567890abc
│   ├── 时长 (秒): 5.50
│   ├── 时间轴位置 (秒): 0.00
│   ├── 结束位置 (秒): 5.50
│   ├── 像素位置: 0
│   ├── 像素宽度: 73
│   ├── 文件大小: 2.5 MB
│   └── 文件类型: video/mp4
├── 片段 2: video2.mp4
└── 片段 3: video3.mp4
```

### 3. 重叠检测
```
⚠️ 重叠检测 (1 个重叠)
├── 重叠 1
│   ├── 片段1: video1.mp4
│   ├── 片段1范围: 0.00s - 5.50s
│   ├── 片段2: video2.mp4
│   ├── 片段2范围: 3.00s - 8.00s
│   └── 重叠区域: 3.00s - 5.50s (2.50s)
└── ✅ 没有检测到重叠 (无重叠时显示)
```

### 4. 时间轴分析
```
📈 时间轴分析
├── 片段总时长 (秒): 15.50
├── 时间轴利用率: 60.8%
├── 空隙数量: 2
├── 平均片段时长 (秒): 5.17
└── 🕳️ 空隙详情
    ├── 空隙 1: 5.50s - 10.00s (4.50s)
    └── 空隙 2: 15.00s - 20.00s (5.00s)
```

### 5. 当前播放状态
```
▶️ 当前播放状态
├── 当前片段: video2.mp4
├── 片段内时间: 2.20s
└── 片段进度: 44.0%

🔇 当前在空白区域 (空白区域时显示)
```

## 🧪 使用场景

### 开发调试
- **性能分析**: 检查像素/秒比例是否合理
- **布局验证**: 确认片段位置和尺寸计算正确
- **重叠排查**: 快速定位重叠问题
- **状态检查**: 验证播放状态和时间同步

### 用户支持
- **问题报告**: 用户可以提供详细的配置信息
- **状态诊断**: 快速了解当前时间轴状态
- **功能验证**: 确认功能是否按预期工作

### 教学演示
- **概念解释**: 展示时间轴的内部结构
- **数据可视化**: 将抽象概念具体化
- **实时反馈**: 操作后立即查看变化

## 🔧 技术细节

### 信息收集
- **实时数据**: 从 Pinia store 获取最新状态
- **计算属性**: 动态计算派生信息
- **文件信息**: 读取原始文件属性

### 格式化输出
- **分组显示**: 使用 `console.group()` 组织信息
- **数值格式**: 自动格式化时间和文件大小
- **状态标识**: 使用图标和颜色区分不同状态

### 性能考虑
- **按需计算**: 只在点击时计算调试信息
- **轻量级**: 不影响正常使用性能
- **无副作用**: 只读取状态，不修改数据

## 📋 使用步骤

### 1. 打开调试
1. 在时间轴中添加一些视频片段
2. 点击工具栏右侧的"调试"按钮
3. 打开浏览器开发者工具 (F12)
4. 查看控制台 (Console) 标签页

### 2. 查看信息
1. 展开各个分组查看详细信息
2. 对比实际显示与调试数据
3. 验证计算结果是否正确

### 3. 问题排查
1. 检查重叠检测结果
2. 验证像素位置计算
3. 确认播放状态同步
4. 分析时间轴利用率

## 💡 调试技巧

### 常用检查点
- **像素/秒比例**: 应该合理，不要过大或过小
- **片段位置**: 像素位置应该与视觉位置一致
- **重叠检测**: 红色片段应该在重叠列表中
- **播放同步**: 当前播放时间应该与视觉指示器一致

### 问题诊断
- **片段不对齐**: 检查像素/秒比例和位置计算
- **播放不同步**: 检查当前播放状态和片段信息
- **重叠未检测**: 检查重叠检测算法和片段范围
- **性能问题**: 检查片段数量和时间轴复杂度

## 🎨 界面设计

### 按钮样式
- **颜色**: 灰色背景，区别于功能按钮
- **边框**: 虚线边框，表示调试性质
- **图标**: 放大镜，表示查看/检查功能
- **位置**: 工具栏右侧，不干扰主要功能

### 控制台输出
- **结构化**: 使用分组和缩进组织信息
- **图标化**: 使用 emoji 图标增强可读性
- **格式化**: 数值自动格式化，易于阅读
- **颜色化**: 利用浏览器控制台的颜色功能

**调试功能让时间轴状态一目了然！** 🔍
