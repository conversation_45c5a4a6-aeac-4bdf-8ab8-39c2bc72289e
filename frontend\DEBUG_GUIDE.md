# 调试指南 - 帧指示器功能

## 🔧 调试步骤

### 1. 打开应用程序
访问：http://localhost:5173/

### 2. 上传视频文件
- 拖拽任意视频文件到时间轴区域
- 确保有视频片段在时间轴上

### 3. 打开浏览器开发者工具
- 按 F12 或右键 -> 检查
- 切换到 Console 标签页

### 4. 手动缩放测试
- 按住 `Alt` 键 + 鼠标滚轮向上滚动
- 观察时间轴的视觉变化

## 🎯 预期的视觉效果

### 最大缩放级别
- 一帧应该占到时间轴宽度的约1/20（5%）
- 在800px宽的时间轴中，一帧约为40px宽

### 帧级别显示
- 时间刻度显示帧数格式（MM:SS:FF）
- 网格线显示帧级别的精细刻度
- 播放头精确对齐到帧边界

## 🎯 检查要点

### 1. 缩放级别是否足够高
- 一帧应该占到容器宽度的 1/20 (5%)
- 在 800px 宽的容器中，一帧应该约为 40px

### 2. 帧级别显示
- 时间刻度应该显示帧数格式
- 网格线应该显示帧级别的精细刻度

### 3. 播放头行为
- 播放头应该精确对齐到帧边界
- 拖拽播放头时应该有帧级精度

## 🐛 常见问题排查

### 问题1：缩放级别不够高
**可能原因：**
- 最大缩放级别计算错误
- 时间轴宽度获取错误

**排查步骤：**
1. 检查一帧是否占到容器宽度的 1/20
2. 确认时间轴宽度值正确
3. 验证缩放计算逻辑

### 问题2：播放头不对齐
**可能原因：**
- 帧对齐算法错误
- 帧率设置不正确

**排查步骤：**
1. 检查帧率设置（默认30fps）
2. 验证播放头是否在帧边界
3. 测试拖拽时的对齐行为

### 问题3：功能不工作
**可能原因：**
- 组件未正确加载
- JavaScript 错误

**排查步骤：**
1. 刷新页面重新测试
2. 确认视频文件已上传
3. 检查浏览器控制台是否有错误

## 🔍 手动检查

如果功能仍然不正常，可以在开发者工具中检查：

1. 在 Elements 标签页中检查时间轴结构
2. 验证网格线是否正确显示
3. 检查播放头元素的位置和样式
4. 确认缩放和滚动事件是否正确绑定

## 📝 报告问题

如果问题仍然存在，请提供以下信息：
1. 浏览器类型和版本
2. 上传的视频文件信息
3. 具体的问题描述和重现步骤
4. 开发者工具中的错误信息（如果有）
