# 🎬 视频预览引擎演示说明

## 快速演示步骤

### 1. 打开应用
访问：`http://localhost:5173/`

### 2. 上传视频（推荐方式）
```
拖拽第一个视频 → 时间轴 0秒位置
拖拽第二个视频 → 时间轴 10秒位置
```

### 3. 开始播放
点击 ▶️ 播放按钮

### 4. 观察效果
- **0-X秒**：播放第一个视频
- **X-10秒**：黑屏 + 时间显示
- **10-Y秒**：播放第二个视频  
- **Y秒后**：继续黑屏

## 🎯 核心特性演示

### ✨ 连续播放
- 时间指针持续移动
- 自动切换视频/黑屏
- 无缝播放体验

### 🎮 交互控制
- 点击时间轴任意位置跳转
- 拖拽视频片段重新排列
- 播放速度调节（0.25x - 2x）

### 📺 视频适配
- 视频完全适配预览窗口
- 保持原始宽高比
- 支持各种尺寸视频

## 💡 最佳演示效果

使用2个短视频（5-10秒），间隔放置，这样可以清楚看到：
1. 视频播放
2. 空白区域黑屏
3. 连续时间轴移动
4. 自动视频切换

**现在就可以开始测试了！** 🚀
