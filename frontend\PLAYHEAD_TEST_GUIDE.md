# 播放头测试指南

## 🎯 播放头行为修复

### 修复的问题
1. **位置对齐**：播放头现在始终对齐到帧的左边界
2. **拖拽功能**：支持鼠标按住播放头进行拖拽
3. **帧精度**：拖拽和点击都会自动对齐到最近的帧边界

## 🔧 测试步骤

### 1. 基础测试
1. 访问：http://localhost:5173/
2. 打开开发者工具 Console
3. 上传一个视频文件
4. 点击"最大缩放"按钮

### 2. 播放头拖拽测试
1. **鼠标悬停**：将鼠标悬停在红色播放头上
   - 光标应该变成 "grab" (手型)
   - 播放头手柄应该稍微变大

2. **开始拖拽**：按住鼠标左键拖拽播放头
   - 光标应该变成 "grabbing"
   - 控制台显示 "🎯 开始拖拽播放头"

3. **拖拽过程**：拖拽时观察
   - 播放头跟随鼠标移动
   - 控制台显示拖拽到的精确时间
   - 播放头始终对齐到帧边界

4. **释放鼠标**：松开鼠标
   - 控制台显示 "🎯 停止拖拽播放头"
   - 播放头停留在最终位置

### 3. 点击跳转测试
1. **点击时间轴**：在时间轴上任意位置点击
   - 播放头应该跳转到点击位置
   - 自动对齐到最近的帧边界

### 4. 帧对齐验证
1. **高缩放级别**：确保缩放到帧级别
2. **观察对齐**：
   - 播放头应该始终在帧指示条的左边界
   - 不应该出现在帧指示条中间或右边

## 📊 预期的调试输出

### 播放头位置计算
```
🎯 播放头位置计算:
  - 原始时间: 1.2345
  - 对齐时间: 1.2333
  - 像素位置: 148.00
```

### 拖拽事件
```
🎯 开始拖拽播放头
🎯 拖拽播放头到: 2.0000 秒
🎯 拖拽播放头到: 2.0333 秒
🎯 停止拖拽播放头
```

### 时间设置
```
⏰ 设置播放时间: 2.0333 秒 (已对齐帧边界)
```

## 🎨 视觉验证

### 播放头外观
- **正常状态**：红色圆形手柄，直径14px
- **悬停状态**：手柄变大到16px，阴影加深
- **拖拽状态**：光标显示为 "grabbing"

### 位置对齐
- **帧级别缩放**：播放头应该精确对齐到帧的左边界
- **黄色指示条**：播放头应该在指示条的左侧，不在中间

## 🐛 问题排查

### 问题1：播放头不能拖拽
**检查项：**
- 播放头的 `pointer-events` 是否为 `auto`
- 是否有 JavaScript 错误阻止事件绑定

### 问题2：播放头位置不对齐
**检查项：**
- 控制台中的 "对齐时间" 是否正确
- 帧率设置是否正确（默认30fps）

### 问题3：拖拽不流畅
**检查项：**
- 是否有其他元素阻挡鼠标事件
- 检查 z-index 层级是否正确

## ✅ 验收标准

- [ ] 播放头可以用鼠标拖拽
- [ ] 拖拽时光标显示正确
- [ ] 播放头始终对齐到帧边界
- [ ] 点击时间轴可以跳转播放头
- [ ] 播放头在帧指示条的左边界
- [ ] 控制台显示正确的调试信息
- [ ] 悬停时播放头有视觉反馈

## 🎮 交互说明

1. **拖拽播放头**：按住红色圆形手柄拖拽
2. **点击跳转**：点击时间轴任意位置
3. **帧精度**：所有操作都自动对齐到帧边界
4. **视觉反馈**：悬停和拖拽时有明确的视觉提示
