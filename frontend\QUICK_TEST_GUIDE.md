# 快速测试指南 - 时间轴缩放和帧指示器

## 🚀 快速开始

1. **启动应用**：
   ```bash
   cd frontend
   npm run dev
   ```
   访问：http://localhost:5173/

2. **打开开发者工具**：
   - 按 F12 或右键 -> 检查
   - 切换到 Console 标签页查看调试信息

3. **上传视频**：
   - 拖拽任意视频文件到时间轴区域
   - 视频片段会出现在时间轴上

4. **快速测试**：
   - 使用 Alt + 鼠标滚轮放大到最大级别
   - 观察帧级别的网格线和时间刻度

## 🔍 测试缩放功能

### 基础缩放测试
1. **放大**：按住 `Alt` 键 + 鼠标滚轮向上滚动
2. **缩小**：按住 `Alt` 键 + 鼠标滚轮向下滚动
3. **观察变化**：
   - 时间刻度间隔会自动调整
   - 网格线密度会改变
   - 视频片段宽度会相应变化

### 帧级别缩放测试
1. **放大到最大**：持续使用 `Alt + 滚轮向上` 直到无法再放大
2. **观察帧级别显示**：
   - 时间刻度显示帧数格式（00:00:01）
   - 网格线显示帧级别的精细刻度
   - 播放头精确对齐到帧边界
3. **测试精度**：
   - 一帧应该占到时间轴宽度的约1/20
   - 播放头拖拽时会自动对齐到帧边界

## ↔️ 测试滚动功能

### 水平滚动测试
1. **先放大**：使用 `Alt + 滚轮` 放大时间轴
2. **水平滚动**：
   - 按住 `Shift` 键 + 鼠标滚轮向上滚动（向左移动）
   - 按住 `Shift` 键 + 鼠标滚轮向下滚动（向右移动）
3. **观察效果**：
   - 时间轴内容会左右移动
   - 播放头位置保持准确
   - 视频片段位置正确更新

## 🎯 精确编辑测试

### 逐帧编辑测试
1. **最大缩放**：放大到能看到帧指示条
2. **精确定位**：
   - 使用帧指示条作为参考
   - 点击时间轴精确定位到帧边界
3. **片段操作**：
   - 拖拽视频片段，观察是否对齐到帧边界
   - 调整片段大小，测试帧级精度

### 组合操作测试
1. **缩放 + 滚动**：
   - 放大后使用滚动查看不同时间段
   - 测试缩放时鼠标位置保持不变
2. **编辑 + 缩放**：
   - 在不同缩放级别下编辑视频片段
   - 验证操作的精确性

## 🎨 视觉效果测试

### 帧指示器外观
- **颜色**：淡黄色渐变（rgba(255, 215, 0, ...)）
- **位置**：紧贴刻度线右侧
- **宽度**：占帧宽度的80%
- **交互**：悬停时颜色加深

### 网格线层次
- **主网格线**：较粗，表示秒级别
- **帧网格线**：较细，表示帧级别
- **指示条**：在时间刻度和时间轴中都显示

## ⚠️ 注意事项

1. **性能**：在极高缩放级别下可能会有轻微性能影响
2. **精度**：帧率假设为30fps，实际视频帧率可能不同
3. **兼容性**：需要支持现代浏览器的CSS渐变和变换

## 🐛 常见问题

1. **指示条不显示**：确保缩放级别足够高（≥300px/秒）
2. **滚动不工作**：检查是否正确按住修饰键（Alt/Shift）
3. **性能问题**：尝试降低缩放级别或刷新页面

## ✅ 验收标准

- [ ] Alt + 滚轮缩放正常工作
- [ ] Shift + 滚轮水平滚动正常工作
- [ ] 帧指示条在高缩放时显示
- [ ] 指示条宽度随缩放动态调整
- [ ] 时间刻度格式根据缩放级别变化
- [ ] 所有交互操作保持精确性
- [ ] 视觉效果符合设计要求
