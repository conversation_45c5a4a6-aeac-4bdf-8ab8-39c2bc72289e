# 时间轴对齐测试指南

## 🎯 测试目标

验证视频片段长度与时间轴刻度完美对齐：
- ✅ 视频片段宽度 = 视频时长 × 像素/秒比例
- ✅ 时间轴刻度 = 时间间隔 × 像素/秒比例
- ✅ 视频片段边界与时间刻度精确对齐

## 🧪 测试步骤

### 1. 准备测试视频
准备几个不同时长的视频：
- **短视频**: 2-3秒
- **中等视频**: 5-10秒  
- **长视频**: 15-20秒

### 2. 对齐测试

#### 测试A：整数秒对齐
1. 拖拽一个**5秒**的视频到**0秒**位置
2. 观察：视频片段右边界应该正好对齐**5秒**刻度线
3. 拖拽一个**10秒**的视频到**10秒**位置
4. 观察：视频片段应该从**10秒**延伸到**20秒**刻度线

#### 测试B：非整数秒对齐
1. 拖拽一个**3.5秒**的视频到**1秒**位置
2. 观察：视频片段应该从**1秒**延伸到**4.5秒**位置
3. 检查片段长度是否准确反映3.5秒的时长

#### 测试C：多片段对齐
1. 放置多个视频片段：
   ```
   [2秒视频] [空隙] [5秒视频] [空隙] [3秒视频]
   0-2秒     2-7秒   7-12秒    12-15秒  15-18秒
   ```
2. 验证每个片段的边界都与对应的时间刻度对齐

### 3. 动态显示测试

#### 宽片段显示（>100px宽度）
- ✅ 显示缩略图
- ✅ 显示文件名
- ✅ 显示时长
- ✅ 显示调整手柄

#### 窄片段显示（<100px宽度）
- ✅ 隐藏缩略图和文件名
- ✅ 只显示时长（居中）
- ✅ 保持调整手柄可用

### 4. 精确度验证

#### 像素级精确度
1. 使用浏览器开发者工具测量
2. 检查视频片段的CSS宽度
3. 计算：`实际宽度 = 视频时长 × (时间轴宽度 / 总时长)`
4. 验证计算结果与显示宽度一致

#### 时间刻度验证
1. 检查主要刻度线位置（每10秒）
2. 检查次要刻度线位置（每1秒或5秒）
3. 验证刻度间距与视频片段宽度比例一致

## 🔧 技术实现验证

### 关键计算公式
```typescript
// 时间轴和视频片段都使用相同的比例计算
const pixelsPerSecond = timelineWidth / totalDuration

// 视频片段宽度
const clipWidth = clip.duration * pixelsPerSecond

// 视频片段位置
const clipLeft = clip.timelinePosition * pixelsPerSecond

// 时间刻度位置
const markPosition = (time / totalDuration) * timelineWidth
```

### 对齐检查点
- [ ] 0秒位置：时间轴起点
- [ ] 主要刻度：10秒、20秒、30秒等
- [ ] 次要刻度：1秒、5秒间隔
- [ ] 视频片段起点：与拖拽位置对齐
- [ ] 视频片段终点：起点 + 视频时长

## 📊 预期效果

### ✅ 正确对齐
```
时间轴: |----|----|----|----|----|----|
刻度:   0    5    10   15   20   25
视频:   [--5秒--]     [--8秒--]
位置:   0-5秒         12-20秒
```

### ❌ 错误对齐
```
时间轴: |----|----|----|----|----|----|
刻度:   0    5    10   15   20   25
视频:   [--过长--]    [--过短-]
问题:   不匹配时长    不匹配时长
```

## 🚀 优化效果

### 最小宽度优化
- **之前**: 最小60px，短视频显示不准确
- **现在**: 最小20px，保持可见性但不影响时间准确性

### 动态显示优化
- **宽片段**: 显示完整信息（缩略图+文件名+时长）
- **窄片段**: 只显示时长，避免信息拥挤

### 响应式对齐
- 窗口大小改变时自动重新计算
- 时间轴缩放时保持精确对齐
- 拖拽操作时实时更新位置

现在视频片段长度应该与时间轴刻度完美对齐！🎯
