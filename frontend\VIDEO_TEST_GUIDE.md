# 视频预览窗口测试指南

## 已修复的问题

✅ **视频不会超出预览窗口**
- 添加了 `object-fit: contain` 确保视频按比例缩放
- 设置了 `overflow: hidden` 防止内容溢出
- 添加了明确的容器边界和尺寸限制

## 测试步骤

### 1. 基本功能测试
1. 打开浏览器访问 `http://localhost:5173/`
2. 你应该看到一个黑色的预览窗口，带有灰色边框
3. 预览窗口中央显示播放图标和提示文字

### 2. 视频上传测试
1. 准备一个视频文件（建议使用 MP4 格式）
2. 将视频文件拖拽到下方的时间轴区域
3. 视频片段应该出现在时间轴上

### 3. 视频适配测试
1. 点击播放按钮开始播放
2. 观察视频是否完全显示在预览窗口内
3. 视频应该：
   - ✅ 不超出预览窗口边界
   - ✅ 保持原始宽高比
   - ✅ 居中显示
   - ✅ 适配窗口大小

### 4. 不同尺寸视频测试
建议测试以下类型的视频：
- **横向视频** (16:9, 1920x1080)
- **竖向视频** (9:16, 1080x1920) 
- **正方形视频** (1:1, 1080x1080)
- **超宽视频** (21:9)

### 5. 窗口调整测试
1. 调整浏览器窗口大小
2. 视频应该自动适配新的窗口尺寸
3. 始终保持在预览窗口边界内

## 技术实现细节

### CSS 关键设置
```css
.video-player {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;  /* 关键：保持比例适配 */
  display: block;
}

.video-container {
  overflow: hidden;     /* 关键：防止溢出 */
  width: 100%;
  height: 100%;
}

.preview-window {
  overflow: hidden;     /* 双重保护 */
  border: 2px solid #333; /* 可视边界 */
}
```

### 容器层级
```
预览区域 (max-height限制)
  └── 预览窗口 (overflow: hidden + 边框)
      └── 视频容器 (flex居中 + overflow: hidden)
          └── 视频元素 (object-fit: contain)
```

## 预期效果

### ✅ 正确效果
- 视频完全显示在黑色预览窗口内
- 视频保持原始宽高比，不变形
- 视频居中显示
- 有黑色背景填充空白区域

### ❌ 如果仍有问题
如果视频仍然超出边界，请检查：
1. 浏览器是否支持 `object-fit: contain`
2. 视频文件是否损坏
3. 浏览器控制台是否有错误信息

## 支持的浏览器
- Chrome 31+
- Firefox 36+
- Safari 10+
- Edge 16+

所有现代浏览器都支持 `object-fit` 属性。
